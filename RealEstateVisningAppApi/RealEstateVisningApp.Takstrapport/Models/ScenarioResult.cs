namespace RealEstateVisningApp.Takstrapport.Models;

/// <summary>
/// Represents the result of a scenario fetching operation
/// </summary>
public class ScenarioResult
{
    /// <summary>
    /// Indicates whether the operation was successful
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// The URL that was processed
    /// </summary>
    public string Url { get; set; } = string.Empty;
    
    /// <summary>
    /// The name of the fetcher that processed this URL
    /// </summary>
    public string FetcherName { get; set; } = string.Empty;
    
    /// <summary>
    /// The domain that was processed
    /// </summary>
    public string Domain { get; set; } = string.Empty;
    
    /// <summary>
    /// The extracted data as a dictionary of key-value pairs
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();
    
    /// <summary>
    /// Any error message if the operation failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// The time when the operation was executed
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// The duration of the operation
    /// </summary>
    public TimeSpan Duration { get; set; }
    
    /// <summary>
    /// Additional metadata about the operation
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();
    
    /// <summary>
    /// Creates a successful result
    /// </summary>
    public static ScenarioResult Success(string url, string fetcherName, string domain, Dictionary<string, object> data, TimeSpan duration)
    {
        return new ScenarioResult
        {
            IsSuccess = true,
            Url = url,
            FetcherName = fetcherName,
            Domain = domain,
            Data = data,
            Duration = duration
        };
    }
    
    /// <summary>
    /// Creates a failed result
    /// </summary>
    public static ScenarioResult Failure(string url, string fetcherName, string domain, string errorMessage, TimeSpan duration)
    {
        return new ScenarioResult
        {
            IsSuccess = false,
            Url = url,
            FetcherName = fetcherName,
            Domain = domain,
            ErrorMessage = errorMessage,
            Duration = duration
        };
    }
}
