using Microsoft.Extensions.Logging;
using RealEstateVisningApp.Takstrapport.Models;
using RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Interfaces;

namespace RealEstateVisningApp.Takstrapport.Services;

/// <summary>
/// Service responsible for orchestrating scenario execution and managing fetchers
/// </summary>
public class ScenarioRunner
{
    private readonly ILogger<ScenarioRunner> _logger;
    private readonly IEnumerable<IScenarioFetcher> _fetchers;

    public ScenarioRunner(ILogger<ScenarioRunner> logger, IEnumerable<IScenarioFetcher> fetchers)
    {
        _logger = logger;
        _fetchers = fetchers;
    }

    /// <summary>
    /// Executes a scenario for a single URL
    /// </summary>
    public async Task<ScenarioResult> RunScenarioAsync(string url, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting scenario execution for URL: {Url}", url);

        if (string.IsNullOrWhiteSpace(url))
        {
            var errorMessage = "URL cannot be null or empty";
            _logger.LogError(errorMessage);
            return ScenarioResult.Failure(url ?? string.Empty, "ScenarioRunner", "unknown", errorMessage, TimeSpan.Zero);
        }

        // Find the appropriate fetcher for this URL
        var fetcher = FindFetcherForUrl(url);
        if (fetcher == null)
        {
            var errorMessage = $"No suitable fetcher found for URL: {url}";
            _logger.LogWarning(errorMessage);
            return ScenarioResult.Failure(url, "ScenarioRunner", ExtractDomain(url), errorMessage, TimeSpan.Zero);
        }

        _logger.LogInformation("Using fetcher {FetcherName} for URL {Url}", fetcher.FetcherName, url);

        try
        {
            var result = await fetcher.FetchDataAsync(url, cancellationToken);
            
            if (result.IsSuccess)
            {
                _logger.LogInformation("Scenario completed successfully for {Url}. Extracted {DataCount} data points", 
                    url, result.Data.Count);
            }
            else
            {
                _logger.LogWarning("Scenario failed for {Url}: {ErrorMessage}", url, result.ErrorMessage);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during scenario execution for {Url}", url);
            return ScenarioResult.Failure(url, fetcher.FetcherName, ExtractDomain(url), ex.Message, TimeSpan.Zero);
        }
    }

    /// <summary>
    /// Executes scenarios for multiple URLs
    /// </summary>
    public async Task<List<ScenarioResult>> RunScenariosAsync(IEnumerable<string> urls, CancellationToken cancellationToken = default)
    {
        var results = new List<ScenarioResult>();
        var urlList = urls.ToList();

        _logger.LogInformation("Starting batch scenario execution for {UrlCount} URLs", urlList.Count);

        foreach (var url in urlList)
        {
            if (cancellationToken.IsCancellationRequested)
            {
                _logger.LogInformation("Scenario execution cancelled");
                break;
            }

            var result = await RunScenarioAsync(url, cancellationToken);
            results.Add(result);

            // Add a small delay between requests to be respectful to the target servers
            await Task.Delay(1000, cancellationToken);
        }

        var successCount = results.Count(r => r.IsSuccess);
        _logger.LogInformation("Batch scenario execution completed. {SuccessCount}/{TotalCount} scenarios succeeded", 
            successCount, results.Count);

        return results;
    }

    /// <summary>
    /// Gets information about all available fetchers
    /// </summary>
    public List<FetcherInfo> GetAvailableFetchers()
    {
        return _fetchers.Select(f => new FetcherInfo
        {
            Name = f.FetcherName,
            TargetDomain = f.TargetDomain
        }).ToList();
    }

    /// <summary>
    /// Finds the appropriate fetcher for a given URL
    /// </summary>
    private IScenarioFetcher? FindFetcherForUrl(string url)
    {
        return _fetchers.FirstOrDefault(f => f.CanHandle(url));
    }

    /// <summary>
    /// Extracts domain from URL
    /// </summary>
    private string ExtractDomain(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host;
        }
        catch
        {
            return "unknown";
        }
    }
}

/// <summary>
/// Information about a fetcher
/// </summary>
public class FetcherInfo
{
    public string Name { get; set; } = string.Empty;
    public string TargetDomain { get; set; } = string.Empty;
}
