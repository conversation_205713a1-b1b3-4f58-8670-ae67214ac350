using Microsoft.Extensions.Logging;
using RealEstateVisningApp.Takstrapport.Services;
using System.Text.Json;

namespace RealEstateVisningApp.Takstrapport;

/// <summary>
/// Main application class for the Takstrapport web scraping tool
/// </summary>
public class TakstrapportApplication
{
    private readonly ILogger<TakstrapportApplication> _logger;
    private readonly ScenarioRunner _scenarioRunner;

    public TakstrapportApplication(ILogger<TakstrapportApplication> logger, ScenarioRunner scenarioRunner)
    {
        _logger = logger;
        _scenarioRunner = scenarioRunner;
    }

    public async Task RunAsync(string[] args)
    {
        _logger.LogInformation("Starting Takstrapport Application");

        try
        {
            if (args.Length == 0)
            {
                await ShowInteractiveMenu();
            }
            else
            {
                await ProcessCommandLineArgs(args);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while running the application");
            Console.WriteLine($"Error: {ex.Message}");
        }

        _logger.LogInformation("Takstrapport Application finished");
    }

    private async Task ShowInteractiveMenu()
    {
        Console.WriteLine();
        Console.WriteLine("=== Takstrapport Web Scraping Tool ===");
        Console.WriteLine();

        while (true)
        {
            Console.WriteLine("Choose an option:");
            Console.WriteLine("1. Scrape a single URL");
            Console.WriteLine("2. Scrape multiple URLs from file");
            Console.WriteLine("3. List available fetchers");
            Console.WriteLine("4. Test a URL (check which fetcher would handle it)");
            Console.WriteLine("5. Exit");
            Console.Write("Enter your choice (1-5): ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    await HandleSingleUrl();
                    break;
                case "2":
                    await HandleMultipleUrls();
                    break;
                case "3":
                    ShowAvailableFetchers();
                    break;
                case "4":
                    await TestUrl();
                    break;
                case "5":
                    Console.WriteLine("Goodbye!");
                    return;
                default:
                    Console.WriteLine("Invalid choice. Please try again.");
                    break;
            }

            Console.WriteLine();
        }
    }

    private async Task ProcessCommandLineArgs(string[] args)
    {
        if (args.Length == 1)
        {
            // Single URL
            var url = args[0];
            await ProcessSingleUrl(url);
        }
        else if (args.Length == 2 && args[0] == "--file")
        {
            // File with multiple URLs
            var filePath = args[1];
            await ProcessUrlsFromFile(filePath);
        }
        else
        {
            ShowUsage();
        }
    }

    private async Task HandleSingleUrl()
    { 
        await ProcessSingleUrl("");
        return;
        Console.Write("Enter URL to scrape: ");
        var url = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(url))
        {
            Console.WriteLine("Invalid URL.");
            return;
        }

        await ProcessSingleUrl(url);
    }

    private async Task HandleMultipleUrls()
    {
        Console.Write("Enter path to file containing URLs (one per line): ");
        var filePath = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(filePath))
        {
            Console.WriteLine("Invalid file path.");
            return;
        }

        await ProcessUrlsFromFile(filePath);
    }

    private async Task ProcessSingleUrl(string url)
    {
        Console.WriteLine($"Processing URL: {url}");
        Console.WriteLine();

        var result = await _scenarioRunner.RunScenarioAsync(url);

        if (result.IsSuccess)
        {
            Console.WriteLine("✅ Scraping successful!");
            Console.WriteLine($"Fetcher: {result.FetcherName}");
            Console.WriteLine($"Domain: {result.Domain}");
            Console.WriteLine($"Duration: {result.Duration.TotalSeconds:F2} seconds");
            Console.WriteLine($"Data points extracted: {result.Data.Count}");
            Console.WriteLine();

            // Show extracted data
            Console.WriteLine("Extracted data:");
            var json = JsonSerializer.Serialize(result.Data, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            Console.WriteLine(json);
        }
        else
        {
            Console.WriteLine("❌ Scraping failed!");
            Console.WriteLine($"Error: {result.ErrorMessage}");
            Console.WriteLine($"Duration: {result.Duration.TotalSeconds:F2} seconds");
        }
    }

    private async Task ProcessUrlsFromFile(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                Console.WriteLine($"File not found: {filePath}");
                return;
            }

            var urls = await File.ReadAllLinesAsync(filePath);
            var validUrls = urls.Where(url => !string.IsNullOrWhiteSpace(url)).ToList();

            Console.WriteLine($"Processing {validUrls.Count} URLs from file...");
            Console.WriteLine();

            var results = await _scenarioRunner.RunScenariosAsync(validUrls);

            // Summary
            var successCount = results.Count(r => r.IsSuccess);
            Console.WriteLine($"Results: {successCount}/{results.Count} successful");
            Console.WriteLine();

            // Save results to file
            var outputPath = Path.ChangeExtension(filePath, ".results.json");
            var json = JsonSerializer.Serialize(results, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
            await File.WriteAllTextAsync(outputPath, json);
            Console.WriteLine($"Results saved to: {outputPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing file: {ex.Message}");
        }
    }

    private void ShowAvailableFetchers()
    {
        var fetchers = _scenarioRunner.GetAvailableFetchers();

        Console.WriteLine("Available fetchers:");
        Console.WriteLine();

        foreach (var fetcher in fetchers)
        {
            Console.WriteLine($"• {fetcher.Name}");
            Console.WriteLine($"  Target Domain: {fetcher.TargetDomain}");
            Console.WriteLine();
        }
    }

    private async Task TestUrl()
    {
        Console.Write("Enter URL to test: ");
        var url = Console.ReadLine();

        if (string.IsNullOrWhiteSpace(url))
        {
            Console.WriteLine("Invalid URL.");
            return;
        }

        var fetchers = _scenarioRunner.GetAvailableFetchers();
        
        // We need to check which fetcher can handle this URL
        // For now, we'll just show the domain extraction
        try
        {
            var uri = new Uri(url);
            Console.WriteLine($"URL: {url}");
            Console.WriteLine($"Domain: {uri.Host}");
            Console.WriteLine($"Scheme: {uri.Scheme}");
            Console.WriteLine();

            // Check which fetcher would handle this
            var matchingFetcher = fetchers.FirstOrDefault(f => 
                uri.Host.Contains(f.TargetDomain, StringComparison.OrdinalIgnoreCase));

            if (matchingFetcher != null)
            {
                Console.WriteLine($"✅ This URL would be handled by: {matchingFetcher.Name}");
            }
            else
            {
                Console.WriteLine("❌ No fetcher available for this domain");
                Console.WriteLine("Available domains:");
                foreach (var fetcher in fetchers)
                {
                    Console.WriteLine($"  • {fetcher.TargetDomain}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Invalid URL: {ex.Message}");
        }
    }

    private void ShowUsage()
    {
        Console.WriteLine("Usage:");
        Console.WriteLine("  RealEstateVisningApp.Takstrapport.exe <url>                    - Scrape a single URL");
        Console.WriteLine("  RealEstateVisningApp.Takstrapport.exe --file <path>           - Scrape URLs from file");
        Console.WriteLine("  RealEstateVisningApp.Takstrapport.exe                         - Interactive mode");
    }
}
