using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Base;

namespace RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Domains;

/// <summary>
/// Example template fetcher for other domains - copy and modify this for new domains
/// </summary>
public class ExampleDomainFetcher : BaseScenarioFetcher
{
    public ExampleDomainFetcher(ILogger<BaseScenarioFetcher> logger, IPlaywright playwright) 
        : base(logger, playwright)
    {
    }

    // TODO: Change this to your target domain
    public override string TargetDomain => "example.com";
    
    // TODO: Change this to a descriptive name for your fetcher
    public override string FetcherName => "Example Domain Fetcher";

    public override bool CanHandle(string url)
    {
        if (!base.CanHandle(url))
            return false;

        // TODO: Add additional URL validation specific to your domain
        // For example, check for specific URL patterns, paths, or parameters
        return true;
    }

    protected override async Task<Dictionary<string, object>> ExecuteFetchLogicAsync(IPage page, string url, CancellationToken cancellationToken)
    {
        var data = new Dictionary<string, object>();

        try
        {
            // Wait for the page to load completely
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // TODO: Implement your specific data extraction logic here
            // Examples:

            // Extract page title
            data["title"] = await GetTextContentSafeAsync(page, "h1");

            // Extract specific elements by CSS selector
            data["description"] = await GetTextContentSafeAsync(page, ".description");

            // Extract attributes
            data["canonicalUrl"] = await GetAttributeSafeAsync(page, "link[rel='canonical']", "href");

            // Extract multiple elements
            var links = new List<string>();
            var linkElements = await page.QuerySelectorAllAsync("a[href]");
            foreach (var link in linkElements.Take(10)) // Limit to first 10 links
            {
                var href = await link.GetAttributeAsync("href");
                if (!string.IsNullOrEmpty(href))
                {
                    links.Add(href);
                }
            }
            data["links"] = links;

            // Extract structured data
            var metadata = new Dictionary<string, string>();
            
            // Meta tags
            var metaDescription = await GetAttributeSafeAsync(page, "meta[name='description']", "content");
            if (!string.IsNullOrEmpty(metaDescription))
                metadata["description"] = metaDescription;

            var metaKeywords = await GetAttributeSafeAsync(page, "meta[name='keywords']", "content");
            if (!string.IsNullOrEmpty(metaKeywords))
                metadata["keywords"] = metaKeywords;

            if (metadata.Any())
                data["metadata"] = metadata;

            // TODO: Add more specific extraction logic based on your domain's structure
            // Examples:
            // - Product information for e-commerce sites
            // - Article content for news sites
            // - Property details for real estate sites
            // - Contact information
            // - Images and media
            // - Pricing information
            // - Availability status

            _logger.LogInformation("Extracted {DataCount} data points from {Domain}", data.Count, TargetDomain);

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during {Domain} specific data extraction", TargetDomain);
            throw;
        }
    }

    public override bool ValidateUrl(string url)
    {
        if (!base.ValidateUrl(url))
            return false;

        try
        {
            var uri = new Uri(url);
            // TODO: Add more specific validation for your domain
            return uri.Host.EndsWith(TargetDomain, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }
}
