using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Base;

namespace RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Domains;

/// <summary>
/// Fetcher specifically designed for finn.no real estate listings
/// </summary>
public class FinnFetcher : BaseScenarioFetcher
{
    public FinnFetcher(ILogger<BaseScenarioFetcher> logger, IPlaywright playwright) 
        : base(logger, playwright)
    {
    }

    public override string TargetDomain => "finn.no";
    public override string FetcherName => "Finn.no Real Estate Fetcher";

    public override bool CanHandle(string url)
    {
        if (!base.CanHandle(url))
            return false;

        // Additional validation for Finn.no real estate URLs
        return url.Contains("/realestate/", StringComparison.OrdinalIgnoreCase) ||
               url.Contains("finnkode=", StringComparison.OrdinalIgnoreCase);
    }

    protected override async Task<Dictionary<string, object>> ExecuteFetchLogicAsync(IPage page, string url, CancellationToken cancellationToken)
    {
        var data = new Dictionary<string, object>();

        try
        {
            // Wait for the page to load completely
            await page.WaitForLoadStateAsync(LoadState.NetworkIdle);

            // Extract basic property information
            data["title"] = await GetTextContentSafeAsync(page, "h1");
            data["price"] = await GetTextContentSafeAsync(page, "[data-testid='price']");
            data["address"] = await GetTextContentSafeAsync(page, "[data-testid='address']");
            
            // Extract property details
            var propertyDetails = new Dictionary<string, string>();
            
            // Try to get property size
            var size = await GetTextContentSafeAsync(page, "[data-testid='size']");
            if (!string.IsNullOrEmpty(size))
                propertyDetails["size"] = size;

            // Try to get number of rooms
            var rooms = await GetTextContentSafeAsync(page, "[data-testid='rooms']");
            if (!string.IsNullOrEmpty(rooms))
                propertyDetails["rooms"] = rooms;

            // Try to get property type
            var propertyType = await GetTextContentSafeAsync(page, "[data-testid='property-type']");
            if (!string.IsNullOrEmpty(propertyType))
                propertyDetails["propertyType"] = propertyType;

            // Try to get year built
            var yearBuilt = await GetTextContentSafeAsync(page, "[data-testid='year-built']");
            if (!string.IsNullOrEmpty(yearBuilt))
                propertyDetails["yearBuilt"] = yearBuilt;

            data["propertyDetails"] = propertyDetails;

            // Extract description
            var description = await GetTextContentSafeAsync(page, "[data-testid='description']");
            if (!string.IsNullOrEmpty(description))
                data["description"] = description;

            // Extract images
            var images = new List<string>();
            var imageElements = await page.QuerySelectorAllAsync("img[src*='finn.no']");
            foreach (var img in imageElements)
            {
                var src = await img.GetAttributeAsync("src");
                if (!string.IsNullOrEmpty(src) && src.Contains("finn.no"))
                {
                    images.Add(src);
                }
            }
            data["images"] = images;

            // Extract Finn code from URL
            var finnCodeMatch = System.Text.RegularExpressions.Regex.Match(url, @"finnkode=(\d+)");
            if (finnCodeMatch.Success)
            {
                data["finnCode"] = finnCodeMatch.Groups[1].Value;
            }

            // Extract contact information
            var contactInfo = new Dictionary<string, string>();
            var contactName = await GetTextContentSafeAsync(page, "[data-testid='contact-name']");
            if (!string.IsNullOrEmpty(contactName))
                contactInfo["name"] = contactName;

            var contactPhone = await GetTextContentSafeAsync(page, "[data-testid='contact-phone']");
            if (!string.IsNullOrEmpty(contactPhone))
                contactInfo["phone"] = contactPhone;

            if (contactInfo.Any())
                data["contact"] = contactInfo;

            // Extract energy rating if available
            var energyRating = await GetTextContentSafeAsync(page, "[data-testid='energy-rating']");
            if (!string.IsNullOrEmpty(energyRating))
                data["energyRating"] = energyRating;

            // Log what we found
            _logger.LogInformation("Extracted {DataCount} data points from Finn.no listing", data.Count);

            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Finn.no specific data extraction");
            throw;
        }
    }

    public override bool ValidateUrl(string url)
    {
        if (!base.ValidateUrl(url))
            return false;

        try
        {
            var uri = new Uri(url);
            return uri.Host.EndsWith("finn.no", StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }
}
