using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Base;

namespace RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Domains;

/// <summary>
/// Fetcher specifically designed for privatmegleren.no real estate listings
/// </summary>
public class PrivatmeglerenFetcher : BaseScenarioFetcher
{
    public PrivatmeglerenFetcher(ILogger<BaseScenarioFetcher> logger, IPlaywright playwright) 
        : base(logger, playwright)
    {
    }

    public override string TargetDomain => "privatmegleren.no";
    public override string FetcherName => "Privatmegleren.no Real Estate Fetcher";

    public override bool CanHandle(string url)
    {
        if (!base.CanHandle(url))
            return false;

        // Additional validation for Privatmegleren.no real estate URLs
        return url.Contains("/eiendom/", StringComparison.OrdinalIgnoreCase);
    }

    protected override async Task<Dictionary<string, object>> ExecuteFetchLogicAsync(IPage inpout, string url, CancellationToken cancellationToken)
    {
        using var playwright = await Playwright.CreateAsync();
        await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
        {
            Headless = false,
        });
        var context = await browser.NewContextAsync();

        var page = await context.NewPageAsync();
        await page.GotoAsync("https://privatmegleren.no/eiendom/haferkamp/3657486");
        await page.GetByRole(AriaRole.Button, new() { Name = "Tillat alle" }).ClickAsync();
        var page1 = await page.RunAndWaitForPopupAsync(async () =>
        {
            var download = await page.RunAndWaitForDownloadAsync(async () =>
            {
                await page.GetByRole(AriaRole.Link, new() { Name = "Digitalformat Vedlegg" }).ClickAsync();
            });
        });
        
        await page1.CloseAsync();
        return new Dictionary<string, object>();
    }

    public override bool ValidateUrl(string url)
    {
        if (!base.ValidateUrl(url))
            return false;

        try
        {
            var uri = new Uri(url);
            return uri.Host.EndsWith("privatmegleren.no", StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }
}
