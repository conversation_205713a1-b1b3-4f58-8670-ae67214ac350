using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using RealEstateVisningApp.Takstrapport.Models;
using RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Interfaces;
using System.Diagnostics;

namespace RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Base;

/// <summary>
/// Base class for all scenario fetchers providing common functionality
/// </summary>
public abstract class BaseScenarioFetcher
{
    protected readonly ILogger<BaseScenarioFetcher> _logger;
    protected readonly IPlaywright _playwright;

    protected BaseScenarioFetcher(ILogger<BaseScenarioFetcher> logger, IPlaywright playwright)
    {
        _logger = logger;
        _playwright = playwright;
    }

    public abstract string TargetDomain { get; }
    public abstract string FetcherName { get; }

    public virtual bool CanHandle(string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return false;

        try
        {
            var uri = new Uri(url);
            return uri.Host.Contains(TargetDomain, StringComparison.OrdinalIgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    public virtual bool ValidateUrl(string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return false;

        try
        {
            var uri = new Uri(url);
            return uri.Scheme == "http" || uri.Scheme == "https";
        }
        catch
        {
            return false;
        }
    }

    public async Task<ScenarioResult> FetchDataAsync(string url, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var domain = ExtractDomain(url);

        _logger.LogInformation("Starting data fetch for {Url} using {FetcherName}", url, FetcherName);

        if (!ValidateUrl(url))
        {
            var errorMessage = $"Invalid URL format: {url}";
            _logger.LogError(errorMessage);
            return ScenarioResult.Failure(url, FetcherName, domain, errorMessage, stopwatch.Elapsed);
        }

        if (!CanHandle(url))
        {
            var errorMessage = $"This fetcher cannot handle the URL: {url}";
            _logger.LogError(errorMessage);
            return ScenarioResult.Failure(url, FetcherName, domain, errorMessage, stopwatch.Elapsed);
        }

        IBrowser? browser = null;
        IPage? page = null;

        try
        {
            // Launch browser
            browser = await _playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = true // Set to false for debugging
            });

            page = await browser.NewPageAsync();
            
            // Set reasonable timeouts
            page.SetDefaultTimeout(30000); // 30 seconds
            page.SetDefaultNavigationTimeout(45000); // 45 seconds

            // Navigate to the URL
            await page.GotoAsync(url, new PageGotoOptions { WaitUntil = WaitUntilState.Load });

            // Execute the specific fetching logic
            var data = await ExecuteFetchLogicAsync(page, url, cancellationToken);

            stopwatch.Stop();
            _logger.LogInformation("Successfully fetched data from {Url} in {Duration}ms", url, stopwatch.ElapsedMilliseconds);

            return ScenarioResult.Success(url, FetcherName, domain, data, stopwatch.Elapsed);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error fetching data from {Url}", url);
            return ScenarioResult.Failure(url, FetcherName, domain, ex.Message, stopwatch.Elapsed);
        }
        finally
        {
            if (page != null)
                await page.CloseAsync();
            if (browser != null)
                await browser.CloseAsync();
        }
    }

    /// <summary>
    /// Implement this method in derived classes to define specific fetching logic
    /// </summary>
    protected abstract Task<Dictionary<string, object>> ExecuteFetchLogicAsync(IPage page, string url, CancellationToken cancellationToken);

    /// <summary>
    /// Helper method to extract domain from URL
    /// </summary>
    protected string ExtractDomain(string url)
    {
        try
        {
            var uri = new Uri(url);
            return uri.Host;
        }
        catch
        {
            return "unknown";
        }
    }

    /// <summary>
    /// Helper method to safely get text content from an element
    /// </summary>
    protected async Task<string> GetTextContentSafeAsync(IPage page, string selector)
    {
        try
        {
            var element = await page.QuerySelectorAsync(selector);
            return element != null ? await element.TextContentAsync() ?? string.Empty : string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Failed to get text content for selector {Selector}: {Error}", selector, ex.Message);
            return string.Empty;
        }
    }

    /// <summary>
    /// Helper method to safely get attribute value from an element
    /// </summary>
    protected async Task<string> GetAttributeSafeAsync(IPage page, string selector, string attribute)
    {
        try
        {
            var element = await page.QuerySelectorAsync(selector);
            return element != null ? await element.GetAttributeAsync(attribute) ?? string.Empty : string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Failed to get attribute {Attribute} for selector {Selector}: {Error}", attribute, selector, ex.Message);
            return string.Empty;
        }
    }


}
