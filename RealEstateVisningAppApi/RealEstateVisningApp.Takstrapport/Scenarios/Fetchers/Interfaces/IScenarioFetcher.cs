using RealEstateVisningApp.Takstrapport.Models;

namespace RealEstateVisningApp.Takstrapport.Scenarios.Fetchers.Interfaces;

/// <summary>
/// Interface for all scenario fetchers that perform web scraping for specific domains
/// </summary>
public interface IScenarioFetcher
{
    /// <summary>
    /// The domain this fetcher is designed to handle (e.g., "finn.no", "example.com")
    /// </summary>
    string TargetDomain { get; }
    
    /// <summary>
    /// A descriptive name for this fetcher
    /// </summary>
    string FetcherName { get; }
    
    /// <summary>
    /// Determines if this fetcher can handle the given URL
    /// </summary>
    /// <param name="url">The URL to check</param>
    /// <returns>True if this fetcher can handle the URL</returns>
    bool CanHandle(string url);
    
    /// <summary>
    /// Executes the data fetching scenario for the given URL
    /// </summary>
    /// <param name="url">The URL to fetch data from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The result of the fetching operation</returns>
    Task<ScenarioResult> FetchDataAsync(string url, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Validates that the URL is properly formatted for this fetcher
    /// </summary>
    /// <param name="url">The URL to validate</param>
    /// <returns>True if the URL is valid for this fetcher</returns>
    bool ValidateUrl(string url);
}
